/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  overflow: hidden;
}

/* App layout */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

/* Header - Clean and minimal */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo h1 {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.action-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.action-button:disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.theme-toggle {
  background: #f5f5f5;
  color: #666;
  min-width: 44px;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

/* Error banner - Clean and subtle */
.error-banner {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.error-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.error-dismiss {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.error-dismiss:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Upload panel - Hidden by default for cleaner look */
.upload-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px 24px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Main content */
.app-main {
  flex: 1;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 16px;
}

/* Split pane styles - Clean divider */
.split-container {
  height: 100%;
  display: flex;
  gap: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.split-container > .gutter {
  background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
  cursor: col-resize;
  transition: all 0.2s ease;
  border-radius: 4px;
  width: 8px !important;
  position: relative;
}

.split-container > .gutter:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(1.2);
}

.split-container > .gutter::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 1px;
}

/* Panel headers - Minimal and clean */
.panel-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  font-weight: 600;
  height: 56px;
  flex-shrink: 0;
  color: #2c3e50;
}

.panel-info {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #7f8c8d;
}

.file-name {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator {
  font-size: 10px;
}

.status-indicator.online {
  color: #27ae60;
}

/* Editor panel - Clean and focused */
.editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 12px 0 0 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.editor-container {
  flex: 1;
  overflow: hidden;
}

/* Preview panel - Clean and modern */
.preview-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 0 12px 12px 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

/* Preview header - Simplified and clean */
.preview-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #2c3e50;
  font-size: 14px;
  min-height: 48px;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preview-mode-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 4px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mode-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.mode-button:hover:not(:disabled) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.mode-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.mode-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.open-link {
  color: #667eea;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.open-link:hover {
  background: rgba(102, 126, 234, 0.1);
  text-decoration: none;
}

.preview-frame-container {
  flex: 1;
  position: relative;
  background: #ffffff;
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
  border-radius: 0 0 12px 0;
}

/* Loading, error, and waiting states - Clean and minimal */
.preview-container.loading,
.preview-container.error,
.preview-container.waiting {
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.loading-content,
.error-content,
.waiting-content {
  text-align: center;
  color: #6c757d;
  max-width: 320px;
  padding: 40px 20px;
}

.loading-content h3,
.error-content h3,
.waiting-content h3 {
  margin: 20px 0 12px;
  color: #495057;
  font-weight: 600;
  font-size: 18px;
}

.loading-content p,
.error-content p,
.waiting-content p {
  margin-bottom: 24px;
  line-height: 1.6;
  font-size: 14px;
  color: #6c757d;
}

.loading-spinner {
  animation: spin 1.5s linear infinite;
  color: #667eea;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  color: #ff6b6b;
  filter: drop-shadow(0 2px 4px rgba(255, 107, 107, 0.3));
}

.phone-icon {
  color: #667eea;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* Web preview styles - Clean and focused */
.web-preview-container {
  flex: 1;
  position: relative;
  background: #ffffff;
  border-radius: 0 0 12px 0;
}

.iframe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 0 0 12px 0;
}

.iframe-overlay .loading-content,
.iframe-overlay .error-content {
  text-align: center;
  color: #495057;
}

.iframe-overlay .error-content {
  color: #ff6b6b;
}

.web-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
  border-radius: 0 0 12px 0;
}

/* Mobile preview styles - Modern and clean */
.mobile-preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 48px;
  padding: 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border-radius: 0 0 12px 0;
}

.phone-frame {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 32px;
  padding: 16px 12px 24px 12px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  width: 280px;
  height: 560px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.phone-frame::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 48px;
  height: 4px;
  background: #dee2e6;
  border-radius: 2px;
}

.phone-frame::after {
  content: '';
  position: absolute;
  top: 10px;
  right: 24px;
  width: 8px;
  height: 8px;
  background: #adb5bd;
  border-radius: 50%;
}

.phone-screen {
  width: 100%;
  height: calc(100% - 16px);
  background: #000000;
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  margin-top: 8px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.mobile-frame {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 24px;
  background: #ffffff;
}

.phone-home-indicator {
  position: absolute;
  bottom: 6px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 3px;
  background: #adb5bd;
  border-radius: 2px;
}

.mobile-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.qr-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 24px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.qr-section h4 {
  margin-bottom: 16px;
  color: #495057;
  font-size: 15px;
  font-weight: 600;
}

.qr-code-placeholder,
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qr-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #666;
}

.qr-code-placeholder.small {
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code-placeholder p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.small-text {
  font-size: 11px !important;
  color: #888 !important;
}

.preview-url {
  font-family: 'Courier New', monospace;
  font-size: 10px !important;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
  max-width: 200px;
}

.preview-url.small {
  font-size: 8px !important;
  padding: 2px 4px;
  max-width: 120px;
}

.debug-info {
  font-size: 11px;
  color: #888;
  margin-top: 8px;
  font-family: 'Courier New', monospace;
}

.retry-button.small {
  padding: 4px 8px;
  font-size: 12px;
}

/* Debug panel styles */
.debug-panel {
  position: fixed;
  bottom: 16px;
  right: 16px;
  z-index: 1000;
  background-color: #2d2d30;
  border: 1px solid #3e3e42;
  border-radius: 8px;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.debug-panel.collapsed {
  padding: 0;
}

.debug-panel.expanded {
  width: 400px;
  max-height: 500px;
  overflow-y: auto;
}

.debug-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  font-size: 12px;
  width: 100%;
  text-align: left;
}

.debug-toggle:hover {
  background-color: #3e3e42;
}

.debug-header {
  border-bottom: 1px solid #3e3e42;
}

.debug-content {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 16px;
}

.debug-section h4 {
  margin-bottom: 8px;
  color: #4fc3f7;
  font-size: 12px;
}

.debug-section pre {
  background-color: #1e1e1e;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 10px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.debug-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.debug-button {
  padding: 4px 8px;
  background-color: #0e639c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
}

.debug-button:hover:not(:disabled) {
  background-color: #1177bb;
}

.debug-button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

/* Expo Snack Embed styles */
.expo-snack-embed {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f5f5f5;
}

.embed-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.snack-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

.code-info {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  z-index: 5;
}

/* Working Snack Preview styles */
.working-snack-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.working-snack-preview.loading,
.working-snack-preview.error {
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #333;
}

.working-snack-preview .preview-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-size: 12px;
  flex-shrink: 0;
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
}

.status-dot.online {
  background-color: #4caf50;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.open-snack-link {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0e639c;
  text-decoration: none;
  font-size: 11px;
}

.open-snack-link:hover {
  text-decoration: underline;
}

.iframe-container {
  flex: 1;
  position: relative;
}

.loading-steps {
  margin-top: 16px;
  text-align: left;
  font-size: 12px;
}

.step {
  margin: 4px 0;
  color: #666;
}

.code-indicator {
  background-color: #2d2d30;
  color: #ffffff;
  padding: 8px 16px;
  font-size: 11px;
  border-top: 1px solid #3e3e42;
  flex-shrink: 0;
}

.code-preview {
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
  word-break: break-word;
}

.demo-notice {
  color: #ffa726;
  font-size: 10px;
}

.fallback-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
}

/* React Native Web Renderer styles */
.react-native-web-renderer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.renderer-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-size: 12px;
  flex-shrink: 0;
}

.renderer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.renderer-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.run-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.run-button:hover:not(:disabled) {
  background-color: #45a049;
}

.run-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-banner {
  background-color: #ffebee;
  color: #c62828;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  border-bottom: 1px solid #ffcdd2;
}

.retry-btn {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.retry-btn:hover {
  background-color: rgba(198, 40, 40, 0.1);
}

.iframe-container {
  flex: 1;
  position: relative;
  background-color: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  color: #666;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

/* Simple React Native Preview styles */
.simple-react-native-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.simple-react-native-preview .preview-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.preview-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #495057;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6c757d;
}

.status-dot.running {
  background-color: #ffc107;
  animation: pulse 1s infinite;
}

.status-dot.idle {
  background-color: #6c757d;
}

.status-success {
  color: #28a745;
}

.status-error {
  color: #dc3545;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.refresh-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.refresh-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.preview-content {
  flex: 1;
  overflow: auto;
  background-color: white;
}

.preview-placeholder,
.preview-error {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
}

.placeholder-content h3,
.preview-error h3 {
  margin-bottom: 8px;
  color: #495057;
}

.placeholder-content p,
.preview-error p {
  margin-bottom: 16px;
  color: #6c757d;
}

.run-button,
.retry-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.run-button:hover,
.retry-button:hover {
  background-color: #218838;
}

.retry-button {
  background-color: #dc3545;
}

.retry-button:hover {
  background-color: #c82333;
}

.preview-output {
  height: 100%;
  padding: 16px;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 6px 16px;
  flex-shrink: 0;
}

.code-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.code-length {
  font-family: 'Courier New', monospace;
}

/* Error Boundary styles */
.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 20px;
}

.error-boundary-content {
  max-width: 600px;
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-boundary .error-icon {
  color: #dc3545;
  margin-bottom: 20px;
}

.error-boundary h1 {
  color: #343a40;
  margin-bottom: 10px;
  font-size: 24px;
}

.error-boundary p {
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.5;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  color: #007bff;
  margin-bottom: 10px;
}

.error-stack {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  color: #495057;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin: 20px 0;
}

.reload-button,
.retry-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.reload-button {
  background-color: #007bff;
  color: white;
}

.reload-button:hover {
  background-color: #0056b3;
}

.retry-button {
  background-color: #6c757d;
  color: white;
}

.retry-button:hover {
  background-color: #545b62;
}

.error-help {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  text-align: left;
}

.error-help p {
  font-weight: 500;
  margin-bottom: 10px;
}

.error-help ul {
  color: #6c757d;
  padding-left: 20px;
}

.error-help li {
  margin-bottom: 5px;
}

/* Loading fallback styles */
.loading-fallback {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #1e1e1e;
  color: #ffffff;
  text-align: center;
}

.loading-fallback .loading-spinner {
  color: #0e639c;
  margin-bottom: 20px;
  animation: spin 1s linear infinite;
}

.loading-fallback h2 {
  margin-bottom: 10px;
  font-size: 24px;
  color: #ffffff;
}

.loading-fallback p {
  color: #cccccc;
  font-size: 16px;
}

/* App error styles */
.app-error {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 20px;
}

.app-error-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.app-error-content h1 {
  color: #dc3545;
  margin-bottom: 15px;
}

.app-error-content p {
  color: #6c757d;
  margin-bottom: 20px;
}

.app-error-content button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.app-error-content button:hover {
  background-color: #0056b3;
}

/* Live React Native Preview styles */
.live-react-native-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.live-react-native-preview .preview-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.live-react-native-preview .preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;
}

.preview-app {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.live-react-native-preview .preview-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 6px 16px;
  flex-shrink: 0;
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #6c757d;
}

.live-preview-error {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #dc3545;
  padding: 40px 20px;
}

.live-preview-error h3 {
  margin: 16px 0 8px;
  color: #dc3545;
}

.live-preview-error p {
  margin-bottom: 20px;
  color: #6c757d;
}

.live-preview-error .retry-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.live-preview-error .retry-button:hover {
  background-color: #c82333;
}

/* Working Preview styles */
.working-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.working-preview.loading {
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f8f9fa;
}

.working-preview .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #6c757d;
}

.working-preview .loading-content h3 {
  color: #495057;
  margin: 0;
}

.working-preview .loading-content p {
  color: #6c757d;
  margin: 0;
}

.working-preview .preview-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  font-size: 12px;
}

.working-preview .preview-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
}

.working-preview .preview-actions {
  display: flex;
  align-items: center;
}

.working-preview .refresh-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.working-preview .refresh-button:hover {
  background-color: #0056b3;
}

.working-preview .preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;
}

.working-preview .preview-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 6px 16px;
  flex-shrink: 0;
}

.working-preview .preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #6c757d;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Production Preview styles */
.production-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.production-preview.loading {
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f8f9fa;
}

.production-preview .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #6c757d;
}

.production-preview .loading-spinner {
  color: #007bff;
  animation: spin 1s linear infinite;
}

.production-preview .loading-content h3 {
  color: #495057;
  margin: 0;
  font-size: 18px;
}

.production-preview .loading-content p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}

.production-preview .preview-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  font-size: 13px;
}

.production-preview .preview-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  font-weight: 500;
}

.production-preview .preview-actions {
  display: flex;
  align-items: center;
}

.production-preview .refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.production-preview .refresh-button:hover {
  background-color: #0056b3;
}

.production-preview .preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;
}

.production-preview .preview-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 8px 16px;
  flex-shrink: 0;
}

.production-preview .preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
}

/* Dynamic Preview styles */
.dynamic-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dynamic-preview.loading,
.dynamic-preview.error {
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f8f9fa;
}

.dynamic-preview .loading-content,
.dynamic-preview .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #6c757d;
}

.dynamic-preview .loading-spinner {
  color: #007bff;
  animation: spin 1s linear infinite;
}

.dynamic-preview .error-content {
  color: #dc3545;
}

.dynamic-preview .loading-content h3,
.dynamic-preview .error-content h3 {
  color: #495057;
  margin: 0;
  font-size: 18px;
}

.dynamic-preview .loading-content p,
.dynamic-preview .error-content p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}

.dynamic-preview .preview-header {
  background-color: #e8f5e8;
  border-bottom: 1px solid #c8e6c9;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  font-size: 13px;
}

.dynamic-preview .preview-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2e7d32;
  font-weight: 600;
}

.dynamic-preview .preview-actions {
  display: flex;
  align-items: center;
}

.dynamic-preview .refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.dynamic-preview .refresh-button:hover {
  background-color: #45a049;
}

.dynamic-preview .preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;
}

.dynamic-preview .preview-footer {
  background-color: #e8f5e8;
  border-top: 1px solid #c8e6c9;
  padding: 8px 16px;
  flex-shrink: 0;
}

.dynamic-preview .preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #2e7d32;
  font-weight: 600;
}

.dynamic-preview .retry-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
}

.dynamic-preview .retry-button:hover {
  background-color: #c82333;
}

/* Smart Preview styles */
.smart-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.smart-preview.loading,
.smart-preview.error {
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f8f9fa;
}

.smart-preview .loading-content,
.smart-preview .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #6c757d;
}

.smart-preview .loading-spinner {
  color: #28a745;
  animation: spin 1s linear infinite;
}

.smart-preview .error-content {
  color: #dc3545;
}

.smart-preview .loading-content h3,
.smart-preview .error-content h3 {
  color: #495057;
  margin: 0;
  font-size: 18px;
}

.smart-preview .loading-content p,
.smart-preview .error-content p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}

.smart-preview .preview-header {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-bottom: 1px solid #a5d6a7;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  font-size: 13px;
}

.smart-preview .preview-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1b5e20;
  font-weight: 600;
}

.smart-preview .preview-actions {
  display: flex;
  align-items: center;
}

.smart-preview .refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.smart-preview .refresh-button:hover {
  background: linear-gradient(135deg, #45a049 0%, #388e3c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

.smart-preview .preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;
}

.smart-preview .preview-footer {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-top: 1px solid #a5d6a7;
  padding: 8px 16px;
  flex-shrink: 0;
}

.smart-preview .preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #1b5e20;
  font-weight: 600;
}

.smart-preview .retry-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
}

.smart-preview .retry-button:hover {
  background-color: #c82333;
}

/* File upload styles */
.file-upload-section {
  max-width: 600px;
}

.dropzone {
  border: 2px dashed #666;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #2d2d30;
}

.dropzone:hover,
.dropzone.active {
  border-color: #0e639c;
  background-color: #1e3a52;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #cccccc;
}

.dropzone-hint {
  font-size: 14px;
  color: #999;
}

.dropzone-formats {
  font-size: 12px;
  color: #777;
}

.uploaded-files {
  margin-top: 16px;
}

.uploaded-files h4 {
  margin-bottom: 8px;
  color: #ffffff;
  font-size: 14px;
}

.uploaded-files ul {
  list-style: none;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #3e3e42;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 14px;
}

.file-name {
  flex: 1;
  color: #ffffff;
}

.file-size {
  color: #999;
  font-size: 12px;
}

.remove-file {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.remove-file:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.file-errors {
  margin-top: 16px;
}

.file-errors h4 {
  color: #f44336;
  margin-bottom: 8px;
  font-size: 14px;
}

.error-item {
  color: #f44336;
  font-size: 14px;
  margin-bottom: 8px;
}

.error-message {
  font-size: 12px;
  color: #ff8a80;
  margin-left: 16px;
}

/* Clean preview content - No headers/footers */
.preview-content-clean {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 0 0 12px 0;
  overflow: hidden;
}

.preview-content-clean.loading,
.preview-content-clean.error {
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.preview-content-clean .loading-content,
.preview-content-clean .error-content {
  text-align: center;
  color: #6c757d;
  max-width: 300px;
  padding: 40px 20px;
}

.preview-content-clean .loading-content h3,
.preview-content-clean .error-content h3 {
  margin: 16px 0 8px;
  color: #495057;
  font-weight: 600;
  font-size: 16px;
}

.preview-content-clean .loading-content p,
.preview-content-clean .error-content p {
  margin-bottom: 20px;
  line-height: 1.5;
  font-size: 14px;
  color: #6c757d;
}

.preview-content-clean .loading-spinner {
  animation: spin 1.5s linear infinite;
  color: #667eea;
  margin-bottom: 16px;
}

.preview-content-clean .retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.preview-content-clean .retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
    height: 56px;
  }

  .logo h1 {
    font-size: 18px;
  }

  .action-button {
    padding: 8px 12px;
    font-size: 13px;
  }

  .panel-header {
    padding: 12px 16px;
    font-size: 14px;
    height: 48px;
  }

  .upload-panel {
    padding: 16px;
  }

  .dropzone {
    padding: 20px 16px;
  }

  .mobile-preview-container {
    flex-direction: column;
    gap: 24px;
    padding: 20px;
  }

  .phone-frame {
    width: 240px;
    height: 480px;
  }

  .split-container {
    flex-direction: column;
  }

  .editor-panel,
  .preview-panel {
    border-radius: 12px;
    margin: 8px;
  }
}
